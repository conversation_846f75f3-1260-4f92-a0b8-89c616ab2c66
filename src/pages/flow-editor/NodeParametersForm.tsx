import React, { useEffect, useState, useRef, useCallback } from "react";
import { useForm } from "react-hook-form";
import type { NodeData, NodeParameter } from "../../service/nodeService";
import { useCredentialsStore } from "../../stores/nodes_store";
import CredentialModal from "../../components/CredentialModal";
import { useFieldVisibility } from "../../hooks/useFieldVisibility";
import FieldRenderer from "../../components/shared/FieldRenderer";
import { PencilSimple } from "phosphor-react";
import { getSavedCredentials, getNodeOptions } from "@/service/commonService";

interface NodeParametersFormProps {
  node: NodeData;
  onSave: (parameters: Record<string, unknown>) => void;
  onCancel: () => void;
}

const NodeParametersForm: React.FC<NodeParametersFormProps> = ({
  node,
  onSave,
  onCancel,
}) => {
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: node.parameters || {},
  });

  const [showCredentialModal, setShowCredentialModal] = useState(false);
  const [credentialModalMode, setCredentialModalMode] = useState<'create' | 'edit'>('create');
  const [selectedCredentialType, setSelectedCredentialType] = useState<any>(null);
  const [editingCredentialId, setEditingCredentialId] = useState<string | null>(null);
  const [savedCredentialsByType, setSavedCredentialsByType] = useState<Record<string, any[]>>({});
  const [selectedCredential, setSelectedCredential] = useState<any>(null);
  const [parameterOptions, setParameterOptions] = useState<Record<string, any[]>>({});
  const [loadingOptions, setLoadingOptions] = useState<Record<string, boolean>>({});
  const credentials = useCredentialsStore((state) => state.credentials);

  // Watch all form values
  const formValues = watch();

  // Reset form when node parameters change
  useEffect(() => {
    reset(node.parameters || {});
  }, [node.parameters, reset]);
  
  // Refs to prevent infinite loops
  const loadingInProgress = useRef<Set<string>>(new Set());
  const previousFormValues = useRef<string>('');
  const credentialsInitialized = useRef<boolean>(false);
  const optionsLoadTimeout = useRef<NodeJS.Timeout | null>(null);

  // Use shared visibility hook for parameters
  const visibleFields = useFieldVisibility(node.nodeType.parameters, formValues);

  // Helper function to check if a parameter is a credential field
  const isCredentialField = useCallback((paramName: string) => {
    return node.nodeType.credentials?.some(cred => cred.name === paramName);
  }, [node.nodeType.credentials]);

  // Load saved credentials - only once
  useEffect(() => {
    if (credentialsInitialized.current || !node.nodeType.credentials) return;
    
    const loadSavedCredentials = async () => {
      const credentialData: Record<string, any[]> = {};

      for (const credentialDef of node.nodeType.credentials!) {
        try {
          const savedCredentials = await getSavedCredentials(credentialDef.name);
          credentialData[credentialDef.name] = savedCredentials;
        } catch (error) {
          credentialData[credentialDef.name] = [];
          console.error(`Failed to load credentials for ${credentialDef.name}:`, error);
        }
      }

      setSavedCredentialsByType(credentialData);
      credentialsInitialized.current = true;
    };

    loadSavedCredentials();
  }, [node.nodeType.credentials]);

  // Debounced options loading function
  const loadOptionsWithDebounce = useCallback(() => {
    // Clear existing timeout
    if (optionsLoadTimeout.current) {
      clearTimeout(optionsLoadTimeout.current);
    }

    // Set new timeout
    optionsLoadTimeout.current = setTimeout(async () => {
      const currentFormString = JSON.stringify(formValues);
      
      // Skip if form values haven't changed
      if (previousFormValues.current === currentFormString) {
        return;
      }
      
      previousFormValues.current = currentFormString;

      // Get parameters with load options
      const parametersWithOptions = node.nodeType.parameters.filter(param => 
        param.type_options?.load_options?.function
      );

      // Process each parameter
      for (const param of parametersWithOptions) {
        if (!visibleFields.has(param.name) || loadingInProgress.current.has(param.name)) {
          continue;
        }

        const dependsOn = param.type_options?.load_options_depends_on || [];
        
        // Check if dependencies are satisfied
        const dependenciesSatisfied = dependsOn.length === 0 || dependsOn.every(depField => {
          if (depField === 'credentials') {
            return node.nodeType.credentials?.some(cred => {
              const credValue = formValues[cred.name];
              return credValue && savedCredentialsByType[cred.name]?.some(
                savedCred => savedCred.id === credValue
              );
            });
          } else if (isCredentialField(depField)) {
            const fieldValue = formValues[depField];
            return fieldValue && savedCredentialsByType[depField]?.some(
              cred => cred.id === fieldValue
            );
          } else {
            const fieldValue = formValues[depField];
            return fieldValue !== undefined && fieldValue !== '' && fieldValue !== null;
          }
        });

        if (!dependenciesSatisfied) {
          setParameterOptions(prev => ({
            ...prev,
            [param.name]: []
          }));
          continue;
        }

        // Mark as loading and load options
        loadingInProgress.current.add(param.name);
        setLoadingOptions(prev => ({ ...prev, [param.name]: true }));

        try {
          // Build credentials object
          const nodeCredentials: Record<string, any> = {};
          
          if (node.nodeType.credentials) {
            for (const credDef of node.nodeType.credentials) {
              const selectedCredId = formValues[credDef.name];
              if (selectedCredId && savedCredentialsByType[credDef.name]) {
                const selectedCredObj = savedCredentialsByType[credDef.name].find(
                  cred => cred.id === selectedCredId
                );
                if (selectedCredObj) {
                  nodeCredentials[credDef.name] = {
                    id: selectedCredObj.id,
                    name: selectedCredObj.name
                  };
                }
              }
            }
          }

          const nodeRequest = {
            name: node.id,
            type: node.nodeType.name,
            display_properties: {
              "x-position": 0,
              "y-position": 0
            },
            is_active: true,
            is_trigger: node.nodeType.name === "manual_trigger",
            parameters: formValues,
            credentials: nodeCredentials
          };

          const response = await getNodeOptions({
            node_request: nodeRequest,
            function: param.type_options!.load_options!.function
          });
          
          // Transform response
          let formattedOptions: any[] = [];
          if (Array.isArray(response)) {
            formattedOptions = response.map(item => {
              if (typeof item === 'object') {
                return {
                  name: item.name || item.label || item.toString(),
                  value: item.value || item.id || item.toString(),
                  description: item.description || ''
                };
              }
              return {
                name: item.toString(),
                value: item.toString(),
                description: ''
              };
            });
          } else if (typeof response === 'object') {
            formattedOptions = Object.entries(response).map(([key, value]) => ({
              name: value?.toString() || key,
              value: key,
              description: ''
            }));
          }

          setParameterOptions(prev => ({
            ...prev,
            [param.name]: formattedOptions
          }));

        } catch (error) {
          console.error(`Failed to load options for ${param.name}:`, error);
          setParameterOptions(prev => ({
            ...prev,
            [param.name]: []
          }));
        } finally {
          setLoadingOptions(prev => ({ ...prev, [param.name]: false }));
          loadingInProgress.current.delete(param.name);
        }
      }
    }, 300); // 300ms debounce
  }, [formValues, savedCredentialsByType, visibleFields, node, isCredentialField]);

  // Trigger options loading when form values change
  useEffect(() => {
    if (credentialsInitialized.current) {
      loadOptionsWithDebounce();
    }
  }, [loadOptionsWithDebounce]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (optionsLoadTimeout.current) {
        clearTimeout(optionsLoadTimeout.current);
      }
    };
  }, []);

  // Handler for credential selection change
  const handleCredentialSelectChange = useCallback((
    event: React.ChangeEvent<HTMLSelectElement>,
    credentialName: string
  ) => {
    if (event.target.value === "create_new") {
      const fullCredentialType = credentials.find(cred => cred.name === credentialName);
      setSelectedCredentialType(fullCredentialType);
      setCredentialModalMode('create');
      setEditingCredentialId(null);
      setShowCredentialModal(true);
      event.target.value = "";
    } else {
      setSelectedCredential(event.target.value);
    }
  }, [credentials]);

  // Handler for edit credential button
  const handleEditCredential = useCallback((credentialName: string) => {
    if (!selectedCredential) return;

    const fullCredentialType = credentials.find(cred => cred.name === credentialName);
    
    if (fullCredentialType) {
      setSelectedCredentialType(fullCredentialType);
      setEditingCredentialId(selectedCredential);
      setCredentialModalMode('edit');
      setShowCredentialModal(true);
    }
  }, [selectedCredential, credentials]);

  const renderCredentialField = useCallback((credentialDef: { name: string; display_name: string; required: boolean }) => {
    const savedCredentialsForType = savedCredentialsByType[credentialDef.name] || [];

    const commonProps = {
      ...register(credentialDef.name, { required: credentialDef.required }),
      className:
        "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
    };

    return (
      <div className="flex items-center gap-1">
        <div className="relative w-[90%]">
          <select
            {...commonProps}
            onChange={(e) => handleCredentialSelectChange(e, credentialDef.name)}
          >
            <option value="">Select Credential</option>
            {savedCredentialsForType.map((credential) => (
              <option key={credential.id} value={credential.id}>
                {credential.name}
              </option>
            ))}
            <option value="create_new" className="border-t border-gray-300 font-medium text-blue-600">
              + Create New API Key
            </option>
          </select>

          {savedCredentialsForType.length === 0 && (
            <p className="text-xs text-gray-500">
              No saved credentials found. Create a new one to get started.
            </p>
          )}
        </div>

        <button
          type="button"
          onClick={() => handleEditCredential(credentialDef.name)}
          disabled={!selectedCredential}
          className={`p-1 rounded-md ${
            selectedCredential 
              ? 'hover:bg-gray-400 text-gray-500' 
              : 'text-gray-300 cursor-not-allowed'
          }`}
          title={selectedCredential ? 'Edit credential' : 'Select a credential to edit'}
        >
          <PencilSimple size={16} weight="light" />
        </button>
      </div>
    );
  }, [savedCredentialsByType, register, handleCredentialSelectChange, handleEditCredential, selectedCredential]);

  const renderField = useCallback((param: NodeParameter) => {
    if (!visibleFields.has(param.name) || isCredentialField(param.name)) {
      return null;
    }

    const fieldOptions = parameterOptions[param.name];
    
    return (
      <FieldRenderer
        field={{
          ...param,
          options: fieldOptions || param.options,
          currentValue: formValues[param.name]
        }}
        register={register}
        errors={errors}
        theme="light"
      />
    );
  }, [visibleFields, isCredentialField, parameterOptions, register, errors, formValues]);

  const handleCloseCredentialModal = useCallback(() => {
    setShowCredentialModal(false);
    setSelectedCredentialType(null);
    setEditingCredentialId(null);
    setCredentialModalMode('create');
    
    // Reset credentials initialization to reload
    credentialsInitialized.current = false;
  }, []);

  const onSubmit = useCallback((data: Record<string, unknown>) => {
    const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value !== "" && value !== null && value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, unknown>);

    onSave(cleanedData);
  }, [onSave]);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          {/* Render credential fields first */}
          {node.nodeType.credentials?.map((credentialDef) => (
            <div key={credentialDef.name} className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Credential to connect with
                {credentialDef.required && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>

              {renderCredentialField(credentialDef)}

              <p className="text-xs text-gray-500">
                Select or create credentials for {credentialDef.display_name}
              </p>

              {errors[credentialDef.name] && (
                <p className="text-xs text-red-500">
                  {credentialDef.required
                    ? `${credentialDef.display_name} is required`
                    : "Invalid value"}
                </p>
              )}
            </div>
          ))}

          {/* Render regular parameter fields */}
          {node.nodeType.parameters.map(
            (param) =>
              visibleFields.has(param.name) && !isCredentialField(param.name) && (
                <div key={param.name}>
                  {renderField(param)}
                </div>
              )
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-md hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Save Parameters
          </button>
        </div>
      </form>

      <CredentialModal
        isOpen={showCredentialModal}
        onClose={handleCloseCredentialModal}
        credentialType={selectedCredentialType}
        credentialId={editingCredentialId}
        mode={credentialModalMode}
      />
    </>
  );
};

export default NodeParametersForm;
