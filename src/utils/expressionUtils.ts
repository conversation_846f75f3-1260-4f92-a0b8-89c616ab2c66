export interface ExpressionValue {
  type: 'fixed' | 'expression';
  value: string;
  originalValue?: unknown;
}

export const parseExpression = (value: string): ExpressionValue => {
  if (typeof value === 'string') {
    // Check for new JavaScript expression format: {expression}
    if (value.startsWith('{') && value.endsWith('}') && !value.startsWith('{{')) {
      return {
        type: 'expression',
        value: value,
      };
    }
    // Check for old expression format: {{ expression }} (for backward compatibility)
    if (value.startsWith('{{') && value.endsWith('}}')) {
      return {
        type: 'expression',
        value: value.slice(2, -2).trim(),
      };
    }
  }
  return {
    type: 'fixed',
    value: value,
  };
};

export const formatExpression = (expression: ExpressionValue): string => {
  if (expression.type === 'expression') {
    // If the value already has braces, return as-is
    if (expression.value.startsWith('{') && expression.value.endsWith('}')) {
      return expression.value;
    }
    // Otherwise, wrap in JavaScript expression braces
    return `{${expression.value}}`;
  }
  return expression.value;
};

export const createExpressionFromDragData = (
  nodeId: string,
  nodeName: string,
  dataPath: string
): string => {
  // Sanitize the node name for JavaScript
  const sanitizedNodeName = nodeName
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/^[0-9]/, '_$&')
    .replace(/_+/g, '_');

  console.log('createExpressionFromDragData:', { nodeId, nodeName, dataPath, sanitizedNodeName });

  // Create a JavaScript expression path
  // Handle array indices properly: convert [0].property to [0].property
  // and handle mixed paths like [0].name or .property[1].subprop
  let cleanPath = dataPath.replace(/^\$\.?/, ''); // Remove leading $. or $

  console.log('After removing $:', { cleanPath });

  // Convert array index notation to proper JavaScript syntax
  // Handle cases like: [0].name, name[0], [0][1].prop, etc.
  cleanPath = cleanPath.replace(/^(\d+)\./, '[$1].'); // Convert leading number.prop to [number].prop
  cleanPath = cleanPath.replace(/\.(\d+)\./g, '[$1].'); // Convert .number. to [number].
  cleanPath = cleanPath.replace(/\.(\d+)$/, '[$1]'); // Convert trailing .number to [number]

  console.log('After regex replacements:', { cleanPath });

  // If the path starts with an array index, we need bracket notation
  if (cleanPath.match(/^\d+/)) {
    cleanPath = `[${cleanPath}]`;
  }

  console.log('After number check:', { cleanPath });

  // If cleanPath starts with [, don't add a dot
  let result;
  if (cleanPath.startsWith('[')) {
    result = `${sanitizedNodeName}${cleanPath}`;
  } else {
    result = `${sanitizedNodeName}.${cleanPath}`;
  }

  console.log('Final result:', { result });
  return result;
};

export const validateExpression = (expression: string): { isValid: boolean; error?: string } => {
  try {
    // Basic validation for expression syntax
    if (!expression.trim()) {
      return { isValid: false, error: 'Expression cannot be empty' };
    }

    // For raw expressions (without {{ }}), just check basic format
    // Should be in format: NodeName.path.to.data
    if (!expression.includes('{{') && !expression.includes('}}')) {
      // Basic format validation - should have at least one dot
      if (!expression.includes('.')) {
        return { isValid: false, error: 'Expression should be in format: NodeName.path.to.data' };
      }
      return { isValid: true };
    }

    // For expressions with brackets, check for balanced brackets
    const openBrackets = (expression.match(/\{/g) || []).length;
    const closeBrackets = (expression.match(/\}/g) || []).length;

    if (openBrackets !== closeBrackets) {
      return { isValid: false, error: 'Unbalanced brackets in expression' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Invalid expression syntax' };
  }
};

export const extractDataPath = (obj: unknown, path: string = '$'): Array<{ path: string; value: unknown; displayName: string }> => {
  const results: Array<{ path: string; value: unknown; displayName: string }> = [];
  
  const traverse = (current: unknown, currentPath: string, displayPath: string) => {
    if (current === null || current === undefined) {
      results.push({
        path: currentPath,
        value: current,
        displayName: displayPath,
      });
      return;
    }
    
    if (typeof current === 'object') {
      if (Array.isArray(current)) {
        current.forEach((item, index) => {
          const newPath = `${currentPath}[${index}]`;
          const newDisplayPath = `${displayPath}[${index}]`;
          traverse(item, newPath, newDisplayPath);
        });
      } else {
        Object.entries(current as Record<string, unknown>).forEach(([key, value]) => {
          const newPath = currentPath === '$' ? `$.${key}` : `${currentPath}.${key}`;
          const newDisplayPath = displayPath === '$' ? key : `${displayPath}.${key}`;
          traverse(value, newPath, newDisplayPath);
        });
      }
    } else {
      results.push({
        path: currentPath,
        value: current,
        displayName: displayPath,
      });
    }
  };
  
  traverse(obj, path, path === '$' ? '' : path);
  return results;
};

export const getValueFromPath = (obj: unknown, path: string): unknown => {
  try {
    if (!path || path === '$') return obj;

    const cleanPath = path.replace(/^\$\.?/, '');
    if (!cleanPath) return obj;

    const parts = cleanPath.split(/\.|\[|\]/).filter(Boolean);
    let current = obj;

    for (const part of parts) {
      if (current === null || current === undefined) return undefined;

      if (typeof current === 'object' && current !== null) {
        if (Array.isArray(current)) {
          const index = parseInt(part, 10);
          if (isNaN(index)) return undefined;
          current = current[index];
        } else {
          current = (current as Record<string, unknown>)[part];
        }
      } else {
        return undefined;
      }
    }

    return current;
  } catch {
    return undefined;
  }
};

// Import the interface from context to avoid duplication
import type { NodeDataForEvaluation } from '../contexts/DragDropContext';

// Expression evaluation result
export interface ExpressionEvaluationResult {
  success: boolean;
  value?: unknown;
  error?: string;
  type?: string;
}

// Evaluate an expression against available node data
export const evaluateExpression = (
  expression: string,
  availableNodes: NodeDataForEvaluation[]
): ExpressionEvaluationResult => {
  try {
    if (!expression.trim()) {
      return { success: false, error: 'Expression is empty' };
    }

    // Parse the expression to extract node reference and path
    // Format: NodeName.path.to.data
    const parts = expression.split('.');
    if (parts.length < 2) {
      return { success: false, error: 'Invalid expression format. Use: NodeName.path.to.data' };
    }

    const nodeName = parts[0].trim();
    const dataPath = parts.slice(1).join('.');

    // Find the referenced node
    const referencedNode = availableNodes.find(node =>
      (node.label || node.nodeType.display_name) === nodeName
    );

    if (!referencedNode) {
      return {
        success: false,
        error: `Node "${nodeName}" not found. Available nodes: ${availableNodes.map(n => n.label || n.nodeType.display_name).join(', ')}`
      };
    }

    // Check if node has execution data
    if (!referencedNode.executionData?.output) {
      return {
        success: false,
        error: `Node "${nodeName}" has no execution data. Run the workflow first.`
      };
    }

    // Evaluate the path against the node's output data
    const result = getValueFromPath(referencedNode.executionData.output, `$.${dataPath}`);

    if (result === undefined) {
      return {
        success: false,
        error: `Path "${dataPath}" not found in node "${nodeName}" output`
      };
    }

    return {
      success: true,
      value: result,
      type: typeof result
    };

  } catch (error) {
    return {
      success: false,
      error: `Evaluation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// Format the evaluation result for display
export const formatEvaluationResult = (result: ExpressionEvaluationResult): string => {
  if (!result.success) {
    return `Error: ${result.error}`;
  }

  if (result.value === null) return 'null';
  if (result.value === undefined) return 'undefined';

  if (typeof result.value === 'string') {
    return `"${result.value}"`;
  }

  if (typeof result.value === 'object') {
    try {
      return JSON.stringify(result.value, null, 2);
    } catch {
      return '[Object]';
    }
  }

  return String(result.value);
};

// Get a short preview of the evaluation result
export const getEvaluationPreview = (result: ExpressionEvaluationResult, maxLength: number = 50): string => {
  const formatted = formatEvaluationResult(result);

  if (formatted.length <= maxLength) {
    return formatted;
  }

  return formatted.substring(0, maxLength - 3) + '...';
};

// Filter out internal metadata from node output data for display
export const filterNodeMetadata = (data: unknown): unknown => {
  if (data && typeof data === 'object' && data !== null && !Array.isArray(data)) {
    return Object.fromEntries(
      Object.entries(data).filter(([key]) => key !== '_nodeMetadata')
    );
  }
  return data;
};

// Calculate node execution data by evaluating all expressions in parameters
export const calculateNodeExecutionData = (
  parameters: Record<string, unknown>,
  availableNodes: NodeDataForEvaluation[]
): {
  evaluatedParameters: Record<string, unknown>;
  executionData: {
    status: 'success' | 'error';
    output?: unknown;
    error?: string;
    startTime: number;
    endTime: number;
  };
} => {
  const startTime = Date.now();
  const evaluatedParameters: Record<string, unknown> = {};
  const errors: string[] = [];

  try {
    // Process each parameter
    Object.entries(parameters).forEach(([key, value]) => {
      if (typeof value === 'string') {
        const parsed = parseExpression(value);

        if (parsed.type === 'expression') {
          // Evaluate the expression
          const evaluation = evaluateJavaScriptExpression(parsed.value, availableNodes);

          if (evaluation.success) {
            evaluatedParameters[key] = evaluation.value;
          } else {
            evaluatedParameters[key] = value; // Keep original if evaluation fails
            errors.push(`Parameter "${key}": ${evaluation.error}`);
          }
        } else {
          // Fixed value, use as-is
          evaluatedParameters[key] = value;
        }
      } else {
        // Non-string values, use as-is
        evaluatedParameters[key] = value;
      }
    });

    const endTime = Date.now();

    // Create execution output that includes both original and evaluated parameters
    const executionOutput = {
      ...evaluatedParameters,
    };

    return {
      evaluatedParameters,
      executionData: {
        status: errors.length > 0 ? 'error' : 'success',
        output: executionOutput,
        error: errors.length > 0 ? errors.join('; ') : undefined,
        startTime,
        endTime
      }
    };

  } catch (error) {
    const endTime = Date.now();
    return {
      evaluatedParameters: parameters,
      executionData: {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error during execution',
        startTime,
        endTime
      }
    };
  }
};

// Validate JavaScript expressions with curly braces
export const validateJavaScriptExpression = (expression: string): { isValid: boolean; error?: string } => {
  try {
    if (!expression.trim()) {
      return { isValid: false, error: 'Expression cannot be empty' };
    }

    // Check for balanced curly braces
    const openBraces = (expression.match(/\{/g) || []).length;
    const closeBraces = (expression.match(/\}/g) || []).length;

    if (openBraces !== closeBraces) {
      return { isValid: false, error: 'Unbalanced curly braces in expression' };
    }

    // Basic syntax validation - should contain at least one expression in braces
    if (!expression.includes('{') || !expression.includes('}')) {
      return { isValid: false, error: 'JavaScript expressions should be wrapped in curly braces: {expression}' };
    }

    // Extract content inside braces for basic validation
    const braceContent = expression.match(/\{([^}]+)\}/g);
    if (!braceContent || braceContent.length === 0) {
      return { isValid: false, error: 'No valid expressions found inside braces' };
    }

    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Invalid JavaScript expression syntax' };
  }
};

// Helper function to sanitize node names for JavaScript identifiers
const sanitizeNodeName = (nodeName: string): string => {
  // Replace spaces and special characters with underscores
  // Keep only alphanumeric characters and underscores
  return nodeName
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/^[0-9]/, '_$&') // Prefix with underscore if starts with number
    .replace(/_+/g, '_'); // Replace multiple underscores with single
};

// Evaluate JavaScript expressions against available node data
export const evaluateJavaScriptExpression = (
  expression: string,
  availableNodes: NodeDataForEvaluation[]
): ExpressionEvaluationResult => {
  try {
    if (!expression.trim()) {
      return { success: false, error: 'Expression is empty' };
    }

    // Extract expressions from curly braces
    const braceMatches = expression.match(/\{([^}]+)\}/g);
    if (!braceMatches || braceMatches.length === 0) {
      return { success: false, error: 'No expressions found in curly braces' };
    }

    // Create a context object with all available node data
    const context: Record<string, unknown> = {};
    const nodeNameMapping: Record<string, string> = {};

    availableNodes.forEach(node => {
      const originalName = node.label || node.nodeType.display_name;
      const sanitizedName = sanitizeNodeName(originalName);

      if (node.executionData?.output) {
        context[sanitizedName] = node.executionData.output;
        nodeNameMapping[originalName] = sanitizedName;
      }
    });

    // Process each expression in braces
    let result = expression;
    for (const match of braceMatches) {
      let innerExpression = match.slice(1, -1); // Remove { and }

      // Replace original node names with sanitized names in the expression
      Object.entries(nodeNameMapping).forEach(([original, sanitized]) => {
        // Use word boundaries to avoid partial replacements
        const regex = new RegExp(`\\b${original.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
        innerExpression = innerExpression.replace(regex, sanitized);
      });

      try {
        // Create a safe evaluation function
        const evalFunction = new Function(...Object.keys(context), `return ${innerExpression}`);
        const evalResult = evalFunction(...Object.values(context));

        // Replace the expression with its result
        result = result.replace(match, String(evalResult));
      } catch (evalError) {
        return {
          success: false,
          error: `Error evaluating "${innerExpression}": ${evalError instanceof Error ? evalError.message : 'Unknown error'}`
        };
      }
    }

    // Try to parse the final result as a number if possible
    const numericResult = Number(result);
    const finalResult = !isNaN(numericResult) && isFinite(numericResult) ? numericResult : result;

    return {
      success: true,
      value: finalResult,
      type: typeof finalResult
    };

  } catch (error) {
    return {
      success: false,
      error: `Evaluation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};
