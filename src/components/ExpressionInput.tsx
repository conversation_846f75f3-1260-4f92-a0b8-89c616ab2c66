import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useDragDrop, type NodeDataForEvaluation } from '../contexts/DragDropContext';
import {
  parseExpression,
  createExpressionFromDragData,
  validateJavaScriptExpression,
  evaluateJavaScriptExpression,
  getEvaluationPreview,
  type ExpressionValue,
  type ExpressionEvaluationResult
} from '../utils/expressionUtils';
import * as Icons from 'lucide-react';

interface ExpressionInputProps {
  name: string;
  value?: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  multiline?: boolean;
  availableNodes?: NodeDataForEvaluation[];
}

const ExpressionInput: React.FC<ExpressionInputProps> = ({
  name,
  value = '',
  onChange,
  onBlur,
  placeholder,
  className = '',
  disabled = false,
  required = false,
  multiline = false,
  availableNodes: propAvailableNodes = [],
}) => {
  const { dragData, isDragging, setDropTarget, availableNodes: contextAvailableNodes } = useDragDrop();

  // Use nodes from props if provided, otherwise use context
  const availableNodes = propAvailableNodes.length > 0 ? propAvailableNodes : contextAvailableNodes;

  // Debug logging for available nodes
  React.useEffect(() => {
    console.log('ExpressionInput availableNodes updated:', {
      propNodesCount: propAvailableNodes.length,
      contextNodesCount: contextAvailableNodes.length,
      finalNodesCount: availableNodes.length,
      finalNodes: availableNodes.map(n => ({ id: n.id, label: n.label, hasOutput: !!n.executionData?.output }))
    });
  }, [propAvailableNodes, contextAvailableNodes, availableNodes]);
  const [mode, setMode] = useState<'fixed' | 'expression'>('fixed');
  const [expressionValue, setExpressionValue] = useState<ExpressionValue>({ type: 'fixed', value: '' });
  const [isDropTarget, setIsDropTarget] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [evaluationResult, setEvaluationResult] = useState<ExpressionEvaluationResult | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  // Helper function to create synthetic events
  const createSyntheticEvent = useCallback((value: string): React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> => {
    return {
      target: { value, name },
      currentTarget: { value, name }
    } as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>;
  }, [name]);

  // Initialize expression value from props
  useEffect(() => {
    // Always sync with the value prop, even if it's empty
    const valueToUse = value || '';
    const parsed = parseExpression(valueToUse);

    // Only update if the parsed value is different from current state
    if (parsed.value !== expressionValue.value || parsed.type !== mode) {
      setExpressionValue(parsed);
      setMode(parsed.type);
    }
  }, [value, expressionValue.value, mode]);

  // Ensure form gets initial value on mount
  const initializedRef = useRef(false);
  useEffect(() => {
    if (!initializedRef.current && !value) {
      onChange(createSyntheticEvent(''));
      initializedRef.current = true;
    }
  }, [value, onChange, createSyntheticEvent]);

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (dragData && dragData.type === 'node-data' && !disabled) {
      setIsDropTarget(true);
      setDropTarget(name);
    }
  };

  // Handle drag leave
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDropTarget(false);
    setDropTarget(null);
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDropTarget(false);
    setDropTarget(null);

    if (dragData && dragData.type === 'node-data' && !disabled) {
      const expression = createExpressionFromDragData(
        dragData.nodeId,
        dragData.nodeName,
        dragData.dataPath
      );

      // Create JavaScript expression format
      const jsExpression = `{${expression}}`;

      const newExpressionValue: ExpressionValue = {
        type: 'expression',
        value: jsExpression,
        originalValue: dragData.value,
      };

      setExpressionValue(newExpressionValue);
      setMode('expression');

      // Pass back the JavaScript expression for the form
      onChange(createSyntheticEvent(jsExpression));
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const newExpressionValue: ExpressionValue = {
      type: mode,
      value: newValue,
      originalValue: expressionValue.originalValue, // Preserve original value
    };

    setExpressionValue(newExpressionValue);

    // Validate and evaluate if in expression mode
    if (mode === 'expression') {
      const validation = validateJavaScriptExpression(newValue);
      setValidationError(validation.isValid ? null : validation.error || null);

      // Evaluate expression if valid and has available nodes
      if (validation.isValid && newValue.trim()) {
        if (availableNodes.length > 0) {
          const evaluation = evaluateJavaScriptExpression(newValue, availableNodes);
          setEvaluationResult(evaluation);
        } else {
          // Show a message when no nodes are available
          setEvaluationResult({
            success: false,
            error: 'No connected nodes available for evaluation. Connect previous nodes to this node first.'
          });
        }
      } else {
        setEvaluationResult(null);
      }
    } else {
      setValidationError(null);
      setEvaluationResult(null);
    }

    // Always return the expression value for the form
    // The form needs a string value to work properly
    onChange(createSyntheticEvent(newValue || ''));
  };

  // Toggle between fixed and expression mode
  const toggleMode = () => {
    const newMode = mode === 'fixed' ? 'expression' : 'fixed';
    setMode(newMode);

    const newExpressionValue: ExpressionValue = {
      type: newMode,
      value: expressionValue.value,
      originalValue: expressionValue.originalValue,
    };

    setExpressionValue(newExpressionValue);

    // Clear validation error and evaluation when switching to fixed mode
    if (newMode === 'fixed') {
      setValidationError(null);
      setEvaluationResult(null);
      onChange(createSyntheticEvent(newExpressionValue.value || ''));
    } else {
      // Evaluate expression when switching to expression mode
      if (expressionValue.value.trim() && availableNodes.length > 0) {
        const validation = validateJavaScriptExpression(expressionValue.value);
        if (validation.isValid) {
          const evaluation = evaluateJavaScriptExpression(expressionValue.value, availableNodes);
          setEvaluationResult(evaluation);
        }
      }

      // For expression mode, return the expression value
      onChange(createSyntheticEvent(newExpressionValue.value || ''));
    }
  };

  const baseClasses = `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${className}`;
  const modeClasses = mode === 'expression' 
    ? 'bg-blue-50 border-blue-300 text-blue-900' 
    : 'bg-white border-gray-300 text-gray-900';
  const dropClasses = isDropTarget && isDragging 
    ? 'border-green-400 bg-green-50 border-2 border-dashed' 
    : '';
  const errorClasses = validationError ? 'border-red-400 bg-red-50' : '';

  const finalClasses = `${baseClasses} ${modeClasses} ${dropClasses} ${errorClasses}`;

  const InputComponent = multiline ? 'textarea' : 'input';

  return (
    <div className="relative">
      <div className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <InputComponent
            ref={inputRef as any}
            type={multiline ? undefined : 'text'}
            value={expressionValue.value}
            onChange={handleInputChange}
            onBlur={onBlur}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            placeholder={placeholder}
            className={finalClasses}
            disabled={disabled}
            required={required}
            rows={multiline ? 3 : undefined}
          />
          
          {/* Mode indicator */}
          {mode === 'expression' && (
            <div className="absolute right-2 top-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
              Expression
            </div>
          )}
        </div>
        
        {/* Mode toggle button */}
        <button
          type="button"
          onClick={toggleMode}
          disabled={disabled}
          className={`p-2 rounded-md border transition-colors ${
            mode === 'expression'
              ? 'bg-blue-100 border-blue-300 text-blue-700 hover:bg-blue-200'
              : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          title={mode === 'expression' ? 'Switch to Fixed Value' : 'Switch to Expression'}
        >
          {mode === 'expression' ? (
            <Icons.Code2 size={16} />
          ) : (
            <Icons.Type size={16} />
          )}
        </button>
      </div>
      
      {/* Validation error */}
      {validationError && (
        <p className="text-xs text-red-500 mt-1 flex items-center space-x-1">
          <Icons.AlertCircle size={12} />
          <span>{validationError}</span>
        </p>
      )}
      
      {/* Drop hint */}
      {isDropTarget && isDragging && (
        <p className="text-xs text-green-600 mt-1 flex items-center space-x-1">
          <Icons.Download size={12} />
          <span>Drop here to create expression</span>
        </p>
      )}

      {/* Debug info (remove in production) */}
      {mode === 'expression' && (
        <div className="mt-1 text-xs text-gray-400 bg-yellow-50 p-2 rounded border">
          Result: {expressionValue?.originalValue !== undefined ? String(expressionValue.originalValue) : 'undefined'}
        </div>
      )}

      {/* Expression evaluation tooltip */}
      {mode === 'expression' && evaluationResult && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              {evaluationResult.success ? (
                <Icons.CheckCircle size={14} className="text-green-600" />
              ) : (
                <Icons.XCircle size={14} className="text-red-600" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <span className="text-xs font-medium text-gray-700">
                  {evaluationResult.success ? 'Result:' : 'Error:'}
                </span>
                {evaluationResult.success && evaluationResult.type && (
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                    {evaluationResult.type}
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-600 font-mono bg-white p-2 rounded border">
                {evaluationResult.success ? (
                  <span className="text-green-700">
                    {getEvaluationPreview(evaluationResult, 100)}
                  </span>
                ) : (
                  <span className="text-red-700">
                    {evaluationResult.error}
                  </span>
                )}
              </div>
              {evaluationResult.success && (
                <button
                  type="button"
                  onClick={() => setShowTooltip(!showTooltip)}
                  className="text-xs text-blue-600 hover:text-blue-800 mt-1"
                >
                  {showTooltip ? 'Show less' : 'Show full result'}
                </button>
              )}
              {showTooltip && evaluationResult.success && (
                <div className="mt-2 text-xs text-gray-600 font-mono bg-white p-2 rounded border max-h-32 overflow-y-auto">
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(evaluationResult.value, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExpressionInput;
